import { Resume, resume } from './resume';
import {
  YamlValidationResult,
  objectToYaml,
  parseYamlWithValidation,
} from './utils.yaml';

// codemirror linter
// from: node.from,
// to: node.to,
// severity: "warning",
// message: "Regular expressions are FORBIDDEN",
// actions: [{
//   name: "Remove",
//   apply(view, from, to) { view.dispatch({changes: {from, to}}) }
// }]

export function yamlToResume(yamlString: string): YamlValidationResult<Resume> {
  return parseYamlWithValidation(
    yamlString,
    resume
  ) as YamlValidationResult<Resume>;
}

export function resumeToYaml(
  resume: Resume,
  options?: {
    indent?: number;
  }
): string {
  return objectToYaml(resume, 'Resume', options);
}
